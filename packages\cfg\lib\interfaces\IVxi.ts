import { BaseItr, BaseMtr, IBase } from './IBase'

import { vixcards } from '../proto'
import { PickField } from 'lib/base'
import { MapProto } from '@wuk/wkp'

export type Vxicard = PickField<vixcards.VxiCard, MapProto<vixcards.VxiCard>>

export abstract class IVxi extends IBase<IVxi> {
  // Device Vixcards Options
  abstract readDeviceVxicards(name: string): Promise<Array<Vxicard>>
  abstract removeDeviceVxicard(name: string): Promise<boolean>
  abstract addDeviceVxicard(val: Vxicard): Promise<boolean>
  abstract modifyDeviceVxicard(name: string, val: Partial<Vxicard>): Promise<boolean>

  static override get NAME() {
    return 'Vxi'
  }

  static get OnVxiCards() {
    return 'Vxi.OnVxiCards'
  }
}

export type VxiItr = BaseItr<IVxi>
export type VxiMtr = BaseMtr<IVxi>
