import { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'
import { IVxi, ICfg, IEngine } from '../interfaces'
import { BaseProxy, method } from './Base'

import { vixcards } from '../proto'
import { Vxicard } from 'lib/interfaces/IVxi'

export class Vxi extends BaseProxy<IVxi> implements IVxi {
  private _vxiCards: Vxicard[]

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(IVxi.NAME, master, win)

    this._vxiCards = []
  }

  @method()
  async readDeviceVxicards(name: string): Promise<Array<Vxicard>> {
    const cfg = await this.loadvxiCardCfg(name)
    return (cfg && cfg.cards) || []
  }

  @method()
  async removeDeviceVxicard(str: string): Promise<boolean> {
    const [name, card_type, card_address] = str.split('.')
    const cfg = await this.loadvxiCardCfg(name)

    if (!cfg) {
      return false
    }

    cfg.cards = cfg.cards.filter(
      card =>
        !(card.name === name && card.card_type === card_type && card.card_address === card_address)
    )

    const changed = await this.writeVxiCardCache(name, cfg)
    if (changed) {
      this.emit(IVxi.OnVxiCards)
    }

    return changed
  }
  @method()
  async addDeviceVxicard(val: Vxicard): Promise<boolean> {
    const cfg = await this.loadvxiCardCfg(val.name)
    if (!cfg) {
      return false
    }

    const cards = cfg.cards

    if (
      cards.find(
        card =>
          card.name === val.name &&
          card.card_type === val.card_type &&
          card.card_address === val.card_address
      )
    ) {
      return false
    }
    const card = new vixcards.VxiCard(val)
    cards.push(card)
    const changed = await this.writeVxiCardCache(val.name, cfg)
    if (changed) {
      this.emit(IVxi.OnVxiCards)
    }

    return changed
  }
  @method()
  async modifyDeviceVxicard(str: string, val: Partial<Vxicard>): Promise<boolean> {
    const [name, card_type, card_address] = str.split('.')
    const cfg = await this.loadvxiCardCfg(name)
    if (!cfg) {
      return false
    }
    cfg.cards = cfg.cards.map(card => {
      return new vixcards.VxiCard(card)
    })
    const card = cfg.cards.find(
      card =>
        card.name === name && card.card_type === card_type && card.card_address === card_address
    )

    if (!card) {
      return false
    }
    card.assign(val)

    const changed = await this.writeVxiCardCache(name, cfg)
    if (changed) {
      this.emit(IVxi.OnVxiCards)
    }

    return changed
  }

  private async writeVxiCardCache(name: string, cfg: vixcards.VxiCards) {
    const fileName = `/${name}.hw.vxi`
    let changed = this.cfg.assign(vixcards.VxiCards, cfg, fileName, this.engineName)
    changed = changed && (await this.cfg.write(vixcards.VxiCards, this.engineName, fileName))

    return changed
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  private get engineName() {
    return this.engine?.engineName || 'vxi'
  }

  private async loadVxiCards(force?: boolean) {
    const { cards = [] } = (await this.cfg.read(vixcards.VxiCards, this.engineName, force)) || {}
    this.emit(IVxi.OnVxiCards)
  }

  private async loadvxiCardCfg(name: string) {
    const fileName = `/${name}.hw.vxi`
    return await this.cfg.read(vixcards.VxiCards, this.engineName, false, fileName)
  }

  private async loadOptions(force?: boolean) {
    // await this.loadVxiCards(force)
  }

  private async clearOptions() {
    this.cfg.remove(vixcards.VxiCards, '', this.engineName)
  }
}
