import { types } from '@wuk/wkp'

export const ArincEngine = {
  $keys: ['name', 'eec', 'speed', 'active'],

  name: types.string,
  eec: types.string,
  speed: types.string,
  active: types.int32
}

export const ArincSignal = {
  $keys: ['channel', 'busstatusparam', 'speed', 'parity', 'active', 'channelid', 'engines'],

  channel: types.int32,
  busstatusparam: types.string,
  speed: types.string,
  parity: types.string,
  active: types.int32,
  channelid: types.string,
  engines: types.arrayOf(ArincEngine)
}

export const Arinc429Cfg = {
  $MAX: 8000,
  $MIN: 10,
  $keys: ['name', 'clock_tic', 'nips', 'index', 'slot', 'address', 'transmitters', 'receivers'],

  name: types.string,
  clock_tic: types.int32,
  nips: types.int32,
  index: types.int32,
  slot: types.int32,
  address: types.int32,
  transmitters: types.arrayOf(ArincSignal),
  receivers: types.arrayOf(ArincSignal)
}
