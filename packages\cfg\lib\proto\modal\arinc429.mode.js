/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import{types}from"@wuk/wkp";let ArincEngine={$keys:["name","eec","speed","active"],name:types.string,eec:types.string,speed:types.string,active:types.int32},ArincSignal={$keys:["channel","busstatusparam","speed","parity","active","channelid","engines"],channel:types.int32,busstatusparam:types.string,speed:types.string,parity:types.string,active:types.int32,channelid:types.string,engines:types.arrayOf(ArincEngine)},Arinc429Cfg={$MAX:8e3,$MIN:10,$keys:["name","clock_tic","nips","index","slot","address","transmitters","receivers"],name:types.string,clock_tic:types.int32,nips:types.int32,index:types.int32,slot:types.int32,address:types.int32,transmitters:types.arrayOf(ArincSignal),receivers:types.arrayOf(ArincSignal)};export{ArincEngine,ArincSignal,Arinc429Cfg};