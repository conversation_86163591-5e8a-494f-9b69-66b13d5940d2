import { BrowserWindow } from 'electron'
import {
  CalsGroupItem,
  DashNumberItem,
  DashOptions,
  DisplayModeType,
  DisplayQuadOption,
  EngineOptions,
  ICfg,
  IEngine,
  kDisplayModeTypeVal,
  kDisplayModeValType,
  TestModeType,
  DisplayCrtOptions,
  CfgFileType
} from '../interfaces'
import { BaseProxy, method } from './Base'
import { main, engine, crts, calcfinal, calcinit, calc } from '../proto'
import { Calc } from './Calc'
import { Table } from './Table'
import { Timer } from './Timer'
import { Display } from './Display'
import { Attribute } from './Attribute'
import { Device } from './Device'
import { fileName } from 'lib/utils'
import { Limit } from './Limit'
import { Vxi } from './Vxi'

export class Engine extends BaseProxy<IEngine> implements IEngine {
  private static readonly kCalibrate = 'calibrate'
  private _calc: Calc
  private _table: Table
  private _timer: Timer
  private _display: Display
  private _attribute: Attribute
  private _device: Device
  private _limit: Limit
  private _vxi: Vxi
  private _engineName: string

  private _options: EngineOptions
  private _dashs: DashOptions
  private _displays: DisplayCrtOptions

  private _calcs_inits: Array<CalsGroupItem>
  private _calcs_finals: Array<CalsGroupItem>

  constructor(private readonly cfg: ICfg, win?: BrowserWindow, master = false) {
    super(IEngine.NAME, master, win)

    this._modules = [
      (this._calc = new Calc(this.cfg, this, win, master)),
      (this._table = new Table(this.cfg, this, win, master)),
      (this._timer = new Timer(this.cfg, this, win, master)),
      (this._display = new Display(this.cfg, this, win, master)),
      (this._attribute = new Attribute(this.cfg, this, win, master)),
      (this._limit = new Limit(this.cfg, this, win, master)),
      (this._device = new Device(this.cfg, this, win, master)),
      (this._vxi = new Vxi(this.cfg, this, win, master))
    ]

    this.property('calc', this._calc)
    this.property('table', this._table)
    this.property('timer', this._timer)
    this.property('display', this._display)
    this.property('attribute', this._attribute)
    this.property('vxi', this._vxi)
    this.property('device', this._device)
    this.property('limit', this._limit)

    this._engineName = ''

    this._options = {
      test_mode: TestModeType.TestWithHardware,
      crs_on_param: '',
      run_limits_param: '',
      pla_idle_default: '',
      pla_takeoff_default: ''
    }

    this._dashs = {
      list: []
    }

    this._displays = {
      list: []
    }

    this._calcs_inits = []
    this._calcs_finals = []
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  get calc() {
    return this._calc
  }

  get table() {
    return this._table
  }

  get timer() {
    return this._timer
  }

  get display() {
    return this._display
  }

  get attribute() {
    return this._attribute
  }

  get device() {
    return this._device
  }

  get limit() {
    return this._limit
  }

  get vxi() {
    return this._vxi
  }

  get engineName() {
    return this._engineName
  }

  @method()
  async openEngine(name: string) {
    this.log('openEngine======name={%1} current={%2}', name, this._engineName)

    if (this._engineName !== name) {
      this._engineName = name

      await this.load(true)
    }
  }

  @method()
  async closeEngine() {
    this.log('closeEngine======engineName={%1}', this._engineName)
    await this.clear()
    this._engineName = ''
  }

  @method()
  async readEngineOptions() {
    return this._options
  }

  @method()
  async writeEngineOptions(val: Partial<EngineOptions>) {
    if (val.test_mode !== undefined) this._options.test_mode = val.test_mode
    if (val.crs_on_param !== undefined) this._options.crs_on_param = val.crs_on_param
    if (val.run_limits_param !== undefined) this._options.run_limits_param = val.run_limits_param
    if (val.pla_idle_default !== undefined) this._options.pla_idle_default = val.pla_idle_default
    if (val.pla_takeoff_default !== undefined)
      this._options.pla_takeoff_default = val.pla_takeoff_default

    const changed = await this.writeEngineCache()
    if (changed) {
      this.emit(IEngine.OnEngineOptions)
    }

    return changed
  }

  private async writeEngineCache() {
    const { test_mode } = this._options

    let changed = this.cfg.assign(
      main.Main,
      {
        ...this._options,
        test_mode: test_mode === TestModeType.TestWithHardware
      },
      '',
      this._engineName
    )
    changed = changed && (await this.cfg.write(main.Main, this._engineName))

    return changed
  }

  @method()
  async readDashOptions() {
    return this._dashs
  }

  private async writeDashCache() {
    const eec: Record<string, engine.Eec> = {}
    const dash: engine.Dash[] = []
    this._dashs.list.forEach((val, index) => {
      const { name, eec_key, option = {} } = val
      const eeckey = `EECKEY${index + 1}`
      eec[eeckey] = new engine.Eec({ ...option })
      dash.push(new engine.Dash({ name, eec_key: eeckey }))
    })

    const changed = this.cfg.assign(engine.Engine, { eec, dash }, '', this._engineName)
    changed && (await this.cfg.write(engine.Engine, this._engineName))

    return changed
  }

  @method()
  async removeDash(index: number) {
    const item = this._dashs.list[index]
    if (!item) {
      return false
    }

    this._dashs.list.splice(index, 1)

    const changed = await this.writeDashCache()
    if (changed) {
      this.emit(IEngine.OnDashOptions)
    }

    return changed
  }

  @method()
  async addDash(val: DashNumberItem, index?: number) {
    if (index === undefined || index === null) {
      this._dashs.list.push(val)
    } else {
      this._dashs.list.splice(index, 0, val)
    }

    const changed = await this.writeDashCache()
    if (changed) {
      this.emit(IEngine.OnDashOptions)
    }

    return changed
  }

  @method()
  async modifyDash(index: number, val: Partial<DashNumberItem>) {
    if (this._dashs.list.length <= index) {
      return false
    }
    const item = this._dashs.list[index]
    if (val.name !== undefined) item.name = val.name
    if (val.option !== undefined) item.option = val.option

    const changed = await this.writeDashCache()
    if (changed) {
      this.emit(IEngine.OnDashOptions)
    }

    return changed
  }

  @method()
  async readDisCrtOptions() {
    return this._displays
  }

  private async writeDisplayCache() {
    const list: crts.Crt[] = []
    this._displays.list.forEach(val => {
      const name = val.name
      const quads = val.quads.map(v => {
        const { position, change_display_mode: display_mode = DisplayModeType.None, list = [] } = v
        const change_display_mode = kDisplayModeValType[display_mode] || ''

        return new crts.Quad({ position, change_display_mode, list })
      })
      list.push(new crts.Crt({ name, quads }))
    })

    const changed = this.cfg.assign(crts.Crts, { crts: list }, '', this._engineName)
    changed && (await this.cfg.write(crts.Crts, this._engineName))

    return changed
  }

  @method()
  async removeDisplayQuad(index: number, quadIdx = 0) {
    const crt = this._displays.list[index]
    crt.quads.splice(quadIdx, 1)

    if (!crt.quads.length) {
      this._displays.list.splice(index, 1)
    }

    crt.quads.forEach((v, idx) => {
      v.position = idx + 1
      v.quad_name = `Quad ${idx + 1}`
    })

    const changed = await this.writeDisplayCache()
    changed && this.emit(IEngine.OnDisplayCrtOptions)

    return changed
  }

  @method()
  async addDisplayCrt(name: string, index?: number) {
    const val = { name, quads: [] }
    if (index === undefined || index === null) {
      this._displays.list.push(val)
    } else {
      this._displays.list.splice(index, 0, val)
    }

    const changed = await this.writeDisplayCache()
    changed && this.emit(IEngine.OnDisplayCrtOptions)

    return changed
  }

  @method()
  async addDisplayQuad(val: DisplayQuadOption, index: number, quadIdx?: number) {
    const crt = this._displays.list[index]
    if (!crt) {
      return false
    }
    if (quadIdx === undefined) {
      crt.quads.push(val)
    } else {
      crt.quads.splice(quadIdx, 0, val)
    }

    crt.quads.forEach((v, idx) => {
      v.position = idx + 1
      v.quad_name = `Quad ${idx + 1}`
    })

    const changed = await this.writeDisplayCache()
    changed && this.emit(IEngine.OnDisplayCrtOptions)

    return changed
  }

  @method()
  async modifyDisplayCrt(index: number, name: string) {
    const crt = this._displays.list[index]
    if (!crt) {
      return false
    }
    crt.name = name

    const changed = await this.writeDisplayCache()
    changed && this.emit(IEngine.OnDisplayCrtOptions)

    return changed
  }

  @method()
  async modifyDisplayQuad(index: number, val: Partial<DisplayQuadOption>, quadIdx: number) {
    const crt = this._displays.list[index]
    if (!crt) {
      return false
    }
    const quad = crt.quads[quadIdx]
    if (!quad) {
      return false
    }
    if (val.quad_name !== undefined) quad.quad_name = val.quad_name
    if (val.change_display_mode !== undefined) quad.change_display_mode = val.change_display_mode
    if (val.list !== undefined) quad.list = val.list

    const changed = await this.writeDisplayCache()
    changed && this.emit(IEngine.OnDisplayCrtOptions)

    return changed
  }

  @method()
  async readCalcsInitOptions() {
    return this._calcs_inits
  }

  @method()
  async removeCalcsInit(index: number) {
    const item = this._calcs_inits[index]
    if (!item) {
      return false
    }
    const isCommon = item.type === CfgFileType.Common
    const engine = isCommon ? 'common' : this.engineName
    if (!(await this.cfg.removeCalc(`${item.group_name}.cal`, engine))) {
      return false
    }
    this.cfg.remove(calc.Calc, item.group_name, engine)
    this._calcs_inits.splice(index, 1)

    const changed = await this.writeCalcsInit()
    changed && this.emit(IEngine.OnCalcsInitOptions)

    return changed
  }

  @method()
  async addCalcsInit(val: CalsGroupItem, index?: number) {
    const name = val.group_name
    const idx = this._calcs_inits.findIndex(v => v.group_name === name)
    if (idx >= 0) {
      return false
    }
    const isCommon = val.type === CfgFileType.Common
    const engine = isCommon ? 'common' : this.engineName
    const file = (name && (await this.cfg.createCalc(name, engine))) || ''
    if (!file) {
      return false
    }
    val.file = isCommon ? `../common/${file}` : file

    if (index === undefined || index === null) {
      this._calcs_inits.push(val)
    } else {
      this._calcs_inits.splice(index, 0, val)
    }

    const changed = await this.writeCalcsInit()
    changed && this.emit(IEngine.OnCalcsInitOptions)

    return changed
  }

  @method()
  async modifyCalcsInit(index: number, val: Partial<CalsGroupItem>) {
    const item = this._calcs_inits[index]
    if (!item) {
      return false
    }
    const name = val.group_name
    if (val.type !== undefined) item.type = val.type
    const isCommon = item.type === CfgFileType.Common
    const engine = isCommon ? 'common' : this.engineName
    if (name && name !== item.group_name) {
      const file = await this.cfg.renameCalc(item.group_name, name, engine)
      if (!file) {
        return false
      }
      val.file = file
    }

    if (name) {
      item.group_name = name
    }
    if (val.file !== undefined) {
      item.file = isCommon ? `../common/${val.file}` : val.file
    }

    const changed = await this.writeCalcsInit()
    changed && this.emit(IEngine.OnCalcsInitOptions)

    return changed
  }

  private async writeCalcsInit() {
    const includes: calcinit.IncludeFile[] = this._calcs_inits.map(
      val => new calcinit.IncludeFile({ file: val.file })
    )
    let changed = this.cfg.assign(calcinit.CalcInit, { includes }, '', this.engineName)
    changed = changed && (await this.cfg.write(calcinit.CalcInit, this.engineName))

    return changed
  }

  @method()
  async readCalcsFinalOptions() {
    return this._calcs_finals
  }

  @method()
  async removeCalcsFinal(index: number) {
    const item = this._calcs_finals[index]
    if (!item) {
      return false
    }

    const engine = item.type === CfgFileType.Common ? 'common' : this.engineName
    if (!(await this.cfg.removeCalc(`${item.group_name}.cal`, engine))) {
      return false
    }
    this.cfg.remove(calc.Calc, item.group_name, engine)
    this._calcs_finals.splice(index, 1)

    const changed = await this.writeCalcsFinal()
    changed && this.emit(IEngine.OnCalcsFinalOptions)

    return changed
  }

  @method()
  async addCalcsFinal(val: CalsGroupItem, index?: number) {
    const name = val.group_name
    const idx = this._calcs_finals.findIndex(v => v.group_name === name)
    if (idx >= 0) {
      return false
    }
    const isCommon = val.type === CfgFileType.Common
    const engine = isCommon ? 'common' : this.engineName
    const file = (name && (await this.cfg.createCalc(name, engine))) || ''
    if (!file) {
      return false
    }
    val.file = isCommon ? `../common/${file}` : file

    if (index === undefined || index === null) {
      this._calcs_finals.push(val)
    } else {
      this._calcs_finals.splice(index, 0, val)
    }

    const changed = await this.writeCalcsFinal()
    changed && this.emit(IEngine.OnCalcsFinalOptions)

    return changed
  }

  @method()
  async modifyCalcsFinal(index: number, val: Partial<CalsGroupItem>) {
    const item = this._calcs_finals[index]
    if (!item) {
      return false
    }
    const name = val.group_name
    if (val.type !== undefined) item.type = val.type
    const isCommon = val.type === CfgFileType.Common
    if (name && name !== item.group_name) {
      const engine = isCommon ? 'common' : this.engineName
      const file = await this.cfg.renameCalc(item.group_name, name, engine)
      if (!file) {
        return false
      }
      item.file = isCommon ? `../common/${file}` : file
    }

    if (name) {
      item.group_name = name
    }

    if (val.file !== undefined) {
      item.file = isCommon ? `../common/${val.file}` : val.file
    }

    const changed = await this.writeCalcsFinal()
    changed && this.emit(IEngine.OnCalcsFinalOptions)

    return changed
  }

  private async writeCalcsFinal() {
    const includes: calcfinal.IncludeFile[] = this._calcs_finals.map(
      val => new calcinit.IncludeFile({ file: val.file })
    )
    let changed = this.cfg.assign(calcfinal.CalcFinal, { includes }, '', this.engineName)
    changed = changed && (await this.cfg.write(calcfinal.CalcFinal, this.engineName))

    return changed
  }

  private async loadEngineOption() {
    if (!this._engineName) {
      return
    }

    this._dashs.list = []
    const { eec = {}, dash = [] } = (await this.cfg.read(engine.Engine, this._engineName)) || {}
    dash.forEach(({ name, eec_key }) => {
      const ec = eec[eec_key]
      ec && this._dashs.list.push({ name, eec_key, option: { ...ec } })
    })
  }

  private async loadCalcsFinalOption() {
    if (!this._engineName) {
      return
    }

    const { includes = [] } = (await this.cfg.read(calcfinal.CalcFinal, this._engineName)) || {}
    this._calcs_finals = includes.map(inc => {
      const file = inc.file
      const group_name = fileName(file)
      const isCommon = file.includes('common/')
      const type = isCommon ? CfgFileType.Common : CfgFileType.EngineSpecific

      return { group_name, type, file }
    })
  }

  private async loadCalcsInitOption() {
    if (!this._engineName) {
      return
    }

    const { includes = [] } = (await this.cfg.read(calcinit.CalcInit, this._engineName)) || {}
    this._calcs_inits = includes.map(inc => {
      const file = inc.file
      const group_name = fileName(file)
      const isCommon = file.includes('common/')
      const type = isCommon ? CfgFileType.Common : CfgFileType.EngineSpecific

      return { group_name, type, file }
    })
  }

  private async loadMainOptions() {
    if (!this._engineName) {
      return
    }

    const {
      scan_rate = '',
      test_mode = true,
      pla_rig_param = '',
      pla_idle_default = '',
      pla_takeoff_default = '',
      crs_on_param = '',
      run_limits_param = ''
    } = (await this.cfg.read(main.Main, this._engineName)) || {}

    this._options = {
      test_mode: test_mode ? TestModeType.TestWithHardware : TestModeType.NoHardware,
      crs_on_param,
      run_limits_param,
      pla_idle_default,
      pla_takeoff_default
    }
  }

  private async loadCrtsOptions() {
    if (!this._engineName) {
      return
    }

    const { crts: list = [] } = (await this.cfg.read(crts.Crts, this._engineName)) || {}
    this._displays.list = []
    list.forEach((crt, index) => {
      const { name, quads: list = [] } = crt
      const quads = list.map(quad => {
        const { position, change_display_mode: mode_type, list = [] } = quad
        const quad_name = `Quad ${position}`
        const change_display_mode: DisplayModeType =
          kDisplayModeTypeVal[mode_type] || DisplayModeType.PullDown
        return { position, quad_name, change_display_mode, list }
      })
      this._displays.list.push({ name, quads })
    })
  }

  private async loadOptions(force?: boolean) {
    await this.loadMainOptions()
    await this.loadEngineOption()
    await this.loadCrtsOptions()
    await this.loadCalcsFinalOption()
    await this.loadCalcsInitOption()
  }

  private async clearOptions() {
    this._display.clear()
    this.cfg.remove(main.Main, '', this.engineName)
    this.cfg.remove(engine.Engine, '', this.engineName)
    this.cfg.remove(crts.Crts, '', this.engineName)
  }
}
