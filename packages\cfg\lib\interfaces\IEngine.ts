import { IAttribute } from './IAttribute'
import { BaseItr, BaseMtr, IBase } from './IBase'
import { ICalc } from './ICalc'
import { IDevice } from './IDevice'
import { IDisplay } from './IDisplay'
import { ILimit } from './ILimit'
import { ITable } from './ITable'
import { ITimer } from './ITimer'
import { IVxi } from './IVxi'
import { DisplayModeType, TestModeType } from './type'

export interface EngineOptions {
  /**
   * Test Mode
   */
  test_mode: TestModeType
  /**
   * Crs On Param
   */
  crs_on_param: string
  /**
   * Run Limits Param
   */
  run_limits_param: string
  /**
   * PLA Idle Default (degs)
   */
  pla_idle_default: string
  /**
   * PLA Takeoff Default (degs)
   */
  pla_takeoff_default: string
}

export interface EECListOption {
  /**
   * EEC Prompt String
   */
  eec_prompt_string: string
  /**
   * EEC Prompt String
   */
  list: string[]
}

export interface DashNumberItem {
  /**
   * Dash Number Name
   */
  name: string
  /**
   * Dash EEC KEY
   */
  eec_key: string
  /**
   * EEC List
   */
  option: EECListOption
}

export interface DashOptions {
  /**
   * Dash Number List
   */
  list: DashNumberItem[]
}

export interface DisplayQuadOption {
  /**
   * Position
   */
  position: number
  /**
   * Quad Name
   */
  quad_name: string
  /**
   * Change Display Mode
   */
  change_display_mode: DisplayModeType
  /**
   * list
   */
  list: string[]
}

export interface DisplayQuadItem {
  /**
   * Name
   */
  name: string
  /**
   * Quad option
   */
  quads: DisplayQuadOption[]
}

export interface DisplayCrtOptions {
  /**
   * Display list
   */
  list: DisplayQuadItem[]
}

export interface CalsGroupItem {
  file: string
  /**
   * Group Name
   */
  group_name: string
  /**
   * Type
   */
  type: number
}

export abstract class IEngine extends IBase<IEngine> {
  abstract get calc(): ICalc
  abstract get table(): ITable
  abstract get timer(): ITimer
  abstract get display(): IDisplay
  abstract get attribute(): IAttribute
  abstract get device(): IDevice
  abstract get limit(): ILimit
  abstract get vxi(): IVxi

  abstract get engineName(): string
  /**
   * Engine Options
   */
  abstract readEngineOptions(): Promise<EngineOptions>
  abstract writeEngineOptions(val: Partial<EngineOptions>): Promise<boolean>

  /**
   * Valid Engine Dash Numbers
   */
  abstract readDashOptions(): Promise<DashOptions>
  abstract removeDash(index: number): Promise<boolean>
  abstract addDash(val: DashNumberItem, index?: number): Promise<boolean>
  abstract modifyDash(index: number, val: Partial<DashNumberItem>): Promise<boolean>

  /**
   * Display list
   */
  abstract readDisCrtOptions(): Promise<DisplayCrtOptions>
  abstract removeDisplayQuad(index: number, quadIdx?: number): Promise<boolean>
  abstract addDisplayCrt(name: string, index?: number): Promise<boolean>
  abstract addDisplayQuad(val: DisplayQuadOption, index: number, quadIdx?: number): Promise<boolean>
  abstract modifyDisplayCrt(index: number, name: string): Promise<boolean>
  abstract modifyDisplayQuad(
    index: number,
    val: Partial<DisplayQuadOption>,
    quadIdx: number
  ): Promise<boolean>
  /**
   * Calcs Initial
   */
  abstract readCalcsInitOptions(): Promise<Array<CalsGroupItem>>
  abstract removeCalcsInit(index: number): Promise<boolean>
  abstract addCalcsInit(val: CalsGroupItem, index?: number): Promise<boolean>
  abstract modifyCalcsInit(index: number, val: Partial<CalsGroupItem>): Promise<boolean>

  /**
   * Calcs Final
   */
  abstract readCalcsFinalOptions(): Promise<Array<CalsGroupItem>>
  abstract removeCalcsFinal(index: number): Promise<boolean>
  abstract addCalcsFinal(val: CalsGroupItem, index?: number): Promise<boolean>
  abstract modifyCalcsFinal(index: number, val: Partial<CalsGroupItem>): Promise<boolean>

  static override get NAME() {
    return 'Engine'
  }

  static get OnEngineOptions() {
    return 'Engine.OnEngineOptions'
  }

  static get OnDashOptions() {
    return 'Engine.OnDashOptions'
  }

  static get OnDisplayCrtOptions() {
    return 'Engine.OnDisplayCrtOptions'
  }

  static get OnCalcsInitOptions() {
    return 'Engine.OnCalcsInitOptions'
  }

  static get OnCalcsFinalOptions() {
    return 'Engine.OnCalcsFinalOptions'
  }

  static get OnCalcsSignalOptions() {
    return 'Engine.OnCalcsSignalOptions'
  }
}

export type EngineMtr = BaseMtr<IEngine>
export type EngineItr = BaseItr<IEngine>
