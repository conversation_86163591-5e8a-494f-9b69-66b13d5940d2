/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  VxiCard as VxiCardModal,
  VxiCards as VxiCardsModal
} from '../modal/vixcards.mode'

export class VxiCard extends MapProto<VxiCard> {
  name = ''
  card_type = ''
  card_address = ''
  no_hardware = false

  constructor(val?: Partial<VxiCard>) {
    super(VxiCardModal, 'VxiCard')
    val && this.assign(val)
  }
}

export class VxiCards extends RootProto<VxiCards> {
  cards: Array<VxiCard> = []

  constructor(val?: Partial<VxiCards>) {
    super(VxiCardsModal, VxiCards.key)
    val && this.assign(val)
  }

  static get maxType() {
    return VxiCardsModal.$MAX
  }

  static get minType() {
    return VxiCardsModal.$MIN
  }

  static get uri() {
    return URI(VxiCardsModal.$MAX, VxiCardsModal.$MIN)
  }

  static get types(): [number, number] {
    return [VxiCardsModal.$MAX, VxiCardsModal.$MIN]
  }

  static get key() {
    return 'VxiCards'
  }
}
