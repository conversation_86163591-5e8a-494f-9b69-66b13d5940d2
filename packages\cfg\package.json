{"name": "@wuk/cfg", "version": "0.0.295", "description": "cfg", "author": "lovisliao", "type": "module", "repository": {"type": "git", "url": "http://git.100vs.com/webs/libs/wksdk"}, "keywords": ["cfg"], "license": "MIT", "main": "./dist/index.js", "types": "./dist/index.d.ts", "directories": {"lib": "dist", "doc": "doc"}, "files": ["dist", "doc"], "engines": {"node": ">=6"}, "maintainers": ["lovisliao", "wang<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "publishConfig": {"access": "public", "registry": "http://npm-registry.100vs.com"}, "scripts": {"dev": "yarn clean && ttsc -w", "build": "yarn clean && ttsc && yarn copy", "clean": "rimraf ./dist", "declare": "tsc -p tsconfig.d.json", "copy": "gulp --gulpfile build/gulp.js", "pub": "yarn build && npm publish --registry=http://npm-registry.100vs.com", "wkp": "rimraf ./lib/proto && node --trace-warnings ../../node_modules/@wuk/wkp/dist/scripts/index.js ts ./build/proto/ ./lib/proto/"}, "dependencies": {"@wuk/base": "^0.0.6", "@wuk/wkp": "^0.0.7", "eventemitter3": "^5.0.0", "date-fns": "^4.1.0", "zeromq": "^6.0.0-beta.6"}, "devDependencies": {"@types/node": "^16.11.9", "electron": "^31.0.0", "gulp": "^4.0.2", "rimraf": "^3.0.2", "ttypescript": "^1.5.13", "typescript-transform-paths": "^3.3.1"}, "eslintIgnore": ["/build", "/proto", "/dist"], "lint-staged": {"lib/**/*.{js,ts}": "eslint --fix"}}