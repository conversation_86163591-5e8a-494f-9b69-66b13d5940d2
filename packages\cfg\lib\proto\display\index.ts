/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  UObject as UObjectModal,
  UBox as UBoxModal,
  ULine as ULineModal,
  UText as UTextModal,
  StringItem as StringItemModal,
  UString as UStringModal,
  DigitalItem as DigitalItemModal,
  UDigital as UDigitalModal,
  BarItem as BarItemModal,
  UBar as UBarModal,
  IndicatorItem as IndicatorItemModal,
  UIndicator as UIndicatorModal,
  UGauge as UGaugeModal,
  CalMsg as CalMsgModal,
  USwitch as USwitchModal,
  UButton as UButtonModal,
  ECMButtonPanel as ECMButtonPanelModal,
  ECMButtonState as ECMButtonStateModal,
  UECMButton as UECMButtonModal,
  XAxis as XAxisModal,
  ReferenceX as ReferenceXModal,
  TablePlot as TablePlotModal,
  Yaxis as YaxisModal,
  HardCopyPlotItem as HardCopyPlotItemModal,
  HardCopyPlot as HardCopyPlotModal,
  UPlot as UPlotModal,
  UFuncButton as UFuncButtonModal,
  UImage as UImageModal,
  UInput as UInputModal,
  ObjectItem as ObjectItemModal,
  Display as DisplayModal
} from '../modal/display.mode'

export class UObject extends MapProto<UObject> {
  start_x = 0
  start_y = 0
  end_x = 0
  end_y = 0
  font = 0.0
  flag_id = ''
  obj_type = ''

  constructor(val?: Partial<UObject>) {
    super(UObjectModal, 'UObject')
    val && this.assign(val)
  }
}

export class UBox extends RootProto<UBox> {
  base = new UObject()
  style = 0
  box_color = ''
  shading = 0
  line_width = 0
  line_color = ''
  box_radius = ''

  constructor(val?: Partial<UBox>) {
    super(UBoxModal, UBox.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UBoxModal.$MAX
  }

  static get minType() {
    return UBoxModal.$MIN
  }

  static get uri() {
    return URI(UBoxModal.$MAX, UBoxModal.$MIN)
  }

  static get types(): [number, number] {
    return [UBoxModal.$MAX, UBoxModal.$MIN]
  }

  static get key() {
    return 'UBox'
  }
}

export class ULine extends RootProto<ULine> {
  base = new UObject()
  line_width = 0
  line_color = ''
  shading = 0

  constructor(val?: Partial<ULine>) {
    super(ULineModal, ULine.key)
    val && this.assign(val)
  }

  static get maxType() {
    return ULineModal.$MAX
  }

  static get minType() {
    return ULineModal.$MIN
  }

  static get uri() {
    return URI(ULineModal.$MAX, ULineModal.$MIN)
  }

  static get types(): [number, number] {
    return [ULineModal.$MAX, ULineModal.$MIN]
  }

  static get key() {
    return 'ULine'
  }
}

export class UText extends RootProto<UText> {
  base = new UObject()
  direction = 0
  alignment = 0
  color = ''
  value = ''
  font_size = 0.0
  font_weight = 0
  item_vec: Array<string> = []

  constructor(val?: Partial<UText>) {
    super(UTextModal, UText.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UTextModal.$MAX
  }

  static get minType() {
    return UTextModal.$MIN
  }

  static get uri() {
    return URI(UTextModal.$MAX, UTextModal.$MIN)
  }

  static get types(): [number, number] {
    return [UTextModal.$MAX, UTextModal.$MIN]
  }

  static get key() {
    return 'UText'
  }
}

export class StringItem extends MapProto<StringItem> {
  color = ''
  op = ''
  value = ''
  text = ''

  constructor(val?: Partial<StringItem>) {
    super(StringItemModal, 'StringItem')
    val && this.assign(val)
  }
}

export class UString extends RootProto<UString> {
  base = new UObject()
  param_id = ''
  label_space = 0
  label = ''
  label_color = ''
  string_color = ''
  param_box = 0
  param_box_color = ''
  shading = 0
  string_font_size = 0.0
  string_font_weight = 0
  string_radius = ''
  label_font_size = 0.0
  label_font_weight = 0
  item_vec: Array<StringItem> = []

  constructor(val?: Partial<UString>) {
    super(UStringModal, UString.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UStringModal.$MAX
  }

  static get minType() {
    return UStringModal.$MIN
  }

  static get uri() {
    return URI(UStringModal.$MAX, UStringModal.$MIN)
  }

  static get types(): [number, number] {
    return [UStringModal.$MAX, UStringModal.$MIN]
  }

  static get key() {
    return 'UString'
  }
}

export class DigitalItem extends MapProto<DigitalItem> {
  param_id = ''
  type = ''
  width = 0
  prec = 0
  label = ''
  units = ''

  constructor(val?: Partial<DigitalItem>) {
    super(DigitalItemModal, 'DigitalItem')
    val && this.assign(val)
  }
}

export class UDigital extends RootProto<UDigital> {
  base = new UObject()
  label_space = 0
  unit_space = 0
  spacing = 0
  param_box = 0
  param_box_color = ''
  label_color = ''
  shading = 0
  direction = 0
  font_size = 0.0
  font_weight = 0
  label_font_size = 0.0
  label_font_weight = 0
  unit_font_size = 0.0
  unit_font_weight = 0
  digit_radius = ''
  item_vec: Array<DigitalItem> = []

  constructor(val?: Partial<UDigital>) {
    super(UDigitalModal, UDigital.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UDigitalModal.$MAX
  }

  static get minType() {
    return UDigitalModal.$MIN
  }

  static get uri() {
    return URI(UDigitalModal.$MAX, UDigitalModal.$MIN)
  }

  static get types(): [number, number] {
    return [UDigitalModal.$MAX, UDigitalModal.$MIN]
  }

  static get key() {
    return 'UDigital'
  }
}

export class BarItem extends MapProto<BarItem> {
  param_id = ''
  target = ''
  corr = ''
  label = ''
  units = ''
  min = 0
  max = 0
  tic_inter = 0
  label_inter = 0

  constructor(val?: Partial<BarItem>) {
    super(BarItemModal, 'BarItem')
    val && this.assign(val)
  }
}

export class UBar extends RootProto<UBar> {
  base = new UObject()
  label_space = 0
  unit_space = 0
  label_color = ''
  length = 0
  bar_height = 0
  format = ''
  tic_pos = ''
  dir = 0
  shading = 0
  spacing = 0
  tic_font = 0
  param_box = 0
  param_box_color = ''
  limit_width = 0
  unit_font_size = 0.0
  tic_font_size = 0.0
  digit_font_size = 0.0
  digit_font_weight = 0
  digit_radius = ''
  label_font_size = 0.0
  label_font_weight = 0
  unit_font_weight = 0
  bar_arc = 0
  item_vec: Array<BarItem> = []

  constructor(val?: Partial<UBar>) {
    super(UBarModal, UBar.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UBarModal.$MAX
  }

  static get minType() {
    return UBarModal.$MIN
  }

  static get uri() {
    return URI(UBarModal.$MAX, UBarModal.$MIN)
  }

  static get types(): [number, number] {
    return [UBarModal.$MAX, UBarModal.$MIN]
  }

  static get key() {
    return 'UBar'
  }
}

export class IndicatorItem extends MapProto<IndicatorItem> {
  color = ''
  op = ''
  test_value_id = ''
  test_string = ''

  constructor(val?: Partial<IndicatorItem>) {
    super(IndicatorItemModal, 'IndicatorItem')
    val && this.assign(val)
  }
}

export class UIndicator extends RootProto<UIndicator> {
  base = new UObject()
  param_id = ''
  text_color = ''
  font_size = 0.0
  font_weight = 0
  radius = ''
  type = ''
  label = ''
  border_color = ''
  box_color = ''
  border_width = 0
  item_vec: Array<IndicatorItem> = []

  constructor(val?: Partial<UIndicator>) {
    super(UIndicatorModal, UIndicator.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UIndicatorModal.$MAX
  }

  static get minType() {
    return UIndicatorModal.$MIN
  }

  static get uri() {
    return URI(UIndicatorModal.$MAX, UIndicatorModal.$MIN)
  }

  static get types(): [number, number] {
    return [UIndicatorModal.$MAX, UIndicatorModal.$MIN]
  }

  static get key() {
    return 'UIndicator'
  }
}

export class UGauge extends RootProto<UGauge> {
  base = new UObject()
  param_id = ''
  gauge_type = ''
  peak_param_id = ''
  label_color = ''
  needle_color = ''
  nums_out_side = 0
  tic_font = 0
  format = ''
  max = 0.0
  min = 0.0
  label = ''
  units = ''
  label_inter = 0
  tic_inter = 0.0
  radius = 0
  param_box = 0
  param_box_color = ''
  shading = 0
  tic_font_size = 0.0
  axis_line_width = 0.0
  axis_line_space = 0.0
  unit_font_size = 0.0
  digit_font_size = 0.0
  digit_font_weight = 0
  digit_radius = ''
  label_font_size = 0.0
  label_font_weight = 0
  unit_font_weight = 0
  gauge_color = ''
  track_color = ''
  inner_ring_color = ''

  constructor(val?: Partial<UGauge>) {
    super(UGaugeModal, UGauge.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UGaugeModal.$MAX
  }

  static get minType() {
    return UGaugeModal.$MIN
  }

  static get uri() {
    return URI(UGaugeModal.$MAX, UGaugeModal.$MIN)
  }

  static get types(): [number, number] {
    return [UGaugeModal.$MAX, UGaugeModal.$MIN]
  }

  static get key() {
    return 'UGauge'
  }
}

export class CalMsg extends MapProto<CalMsg> {
  uuid = ''
  line_num = 0
  path = ''

  constructor(val?: Partial<CalMsg>) {
    super(CalMsgModal, 'CalMsg')
    val && this.assign(val)
  }
}

export class USwitch extends RootProto<USwitch> {
  base = new UObject()
  param_id = ''
  type = ''
  on_label = ''
  off_label = ''
  off_color = ''
  on_color = ''
  hide_box_shadow = 0
  radius = ''
  font_size = 0.0
  font_weight = 0
  has_release = false
  release_msg = new CalMsg()
  release_vec: Array<string> = []
  has_push = false
  push_msg = new CalMsg()
  push_vec: Array<string> = []

  constructor(val?: Partial<USwitch>) {
    super(USwitchModal, USwitch.key)
    val && this.assign(val)
  }

  static get maxType() {
    return USwitchModal.$MAX
  }

  static get minType() {
    return USwitchModal.$MIN
  }

  static get uri() {
    return URI(USwitchModal.$MAX, USwitchModal.$MIN)
  }

  static get types(): [number, number] {
    return [USwitchModal.$MAX, USwitchModal.$MIN]
  }

  static get key() {
    return 'USwitch'
  }
}

export class UButton extends RootProto<UButton> {
  base = new UObject()
  type = ''
  param_id = ''
  set_value = 0
  on_label = ''
  off_label = ''
  label_color = ''
  on_color = ''
  off_color = ''
  radius = ''
  font_size = 0.0
  font_weight = 0

  constructor(val?: Partial<UButton>) {
    super(UButtonModal, UButton.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UButtonModal.$MAX
  }

  static get minType() {
    return UButtonModal.$MIN
  }

  static get uri() {
    return URI(UButtonModal.$MAX, UButtonModal.$MIN)
  }

  static get types(): [number, number] {
    return [UButtonModal.$MAX, UButtonModal.$MIN]
  }

  static get key() {
    return 'UButton'
  }
}

export class ECMButtonPanel extends MapProto<ECMButtonPanel> {
  style = 0
  line_width = 0.0
  line_color = ''
  box_color = ''
  shading = 0.0
  label = ''

  constructor(val?: Partial<ECMButtonPanel>) {
    super(ECMButtonPanelModal, 'ECMButtonPanel')
    val && this.assign(val)
  }
}

export class ECMButtonState extends MapProto<ECMButtonState> {
  state = 0
  label = ''
  on_color = ''
  has_wait = false
  wait_msg = new CalMsg()
  wait_vec: Array<string> = []
  has_lock = false
  lock_msg = new CalMsg()
  lock_vec: Array<string> = []
  has_init = false
  init_msg = new CalMsg()
  init_cal_vec: Array<string> = []
  has_main = false
  main_msg = new CalMsg()
  main_cal_vec: Array<string> = []
  has_final = false
  final_msg = new CalMsg()
  final_cal_vec: Array<string> = []

  constructor(val?: Partial<ECMButtonState>) {
    super(ECMButtonStateModal, 'ECMButtonState')
    val && this.assign(val)
  }
}

export class UECMButton extends RootProto<UECMButton> {
  base = new UObject()
  param_id = ''
  wait_param_id = ''
  type = ''
  label_color = ''
  warn_color = ''
  off_color = ''
  radius = ''
  font_size = 0.0
  font_weight = 0
  default_state = 0
  has_panel = false
  panel = new ECMButtonPanel()
  state_vec: Array<ECMButtonState> = []

  constructor(val?: Partial<UECMButton>) {
    super(UECMButtonModal, UECMButton.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UECMButtonModal.$MAX
  }

  static get minType() {
    return UECMButtonModal.$MIN
  }

  static get uri() {
    return URI(UECMButtonModal.$MAX, UECMButtonModal.$MIN)
  }

  static get types(): [number, number] {
    return [UECMButtonModal.$MAX, UECMButtonModal.$MIN]
  }

  static get key() {
    return 'UECMButton'
  }
}

export class XAxis extends MapProto<XAxis> {
  param_id = ''
  x_start = ''
  x_end = ''
  tic_interval = ''
  y_intercept = ''
  axis_color = ''
  label_interval = ''
  tic_position = ''
  label_position = ''

  constructor(val?: Partial<XAxis>) {
    super(XAxisModal, 'XAxis')
    val && this.assign(val)
  }
}

export class ReferenceX extends MapProto<ReferenceX> {
  y_intercept = 0
  tic_position = ''
  label_position = ''

  constructor(val?: Partial<ReferenceX>) {
    super(ReferenceXModal, 'ReferenceX')
    val && this.assign(val)
  }
}

export class TablePlot extends MapProto<TablePlot> {
  color = ''
  table_name = ''

  constructor(val?: Partial<TablePlot>) {
    super(TablePlotModal, 'TablePlot')
    val && this.assign(val)
  }
}

export class Yaxis extends MapProto<Yaxis> {
  param_id = ''
  y_start = ''
  y_end = ''
  tic_interval = ''
  x_intercept = ''
  axis_color = ''
  label_interval = ''
  grid = ''
  tic_position = ''
  label_position = ''
  bottom_to_x_axis = ''
  top_to_x_axis = ''
  param_list: Array<string> = []
  color_vec: Array<string> = []
  table_plot_vec: Array<TablePlot> = []

  constructor(val?: Partial<Yaxis>) {
    super(YaxisModal, 'Yaxis')
    val && this.assign(val)
  }
}

export class HardCopyPlotItem extends MapProto<HardCopyPlotItem> {
  position_x = 0.0
  position_y = 0.0
  text_string = ''

  constructor(val?: Partial<HardCopyPlotItem>) {
    super(HardCopyPlotItemModal, 'HardCopyPlotItem')
    val && this.assign(val)
  }
}

export class HardCopyPlot extends MapProto<HardCopyPlot> {
  pen_number = 0
  direction = 0
  plot_item_vec: Array<HardCopyPlotItem> = []

  constructor(val?: Partial<HardCopyPlot>) {
    super(HardCopyPlotModal, 'HardCopyPlot')
    val && this.assign(val)
  }
}

export class UPlot extends RootProto<UPlot> {
  base = new UObject()
  plot_key = ''
  type = ''
  points: Array<string> = []
  spot_param_id = ''
  line_width = 0
  hide_button = 0
  has_x_axis = false
  x_axis = new XAxis()
  has_reference_x = false
  reference_x = new ReferenceX()
  yaxis_vec: Array<Yaxis> = []
  has_hard_copy_plot = false
  hard_copy_plot = new HardCopyPlot()

  constructor(val?: Partial<UPlot>) {
    super(UPlotModal, UPlot.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UPlotModal.$MAX
  }

  static get minType() {
    return UPlotModal.$MIN
  }

  static get uri() {
    return URI(UPlotModal.$MAX, UPlotModal.$MIN)
  }

  static get types(): [number, number] {
    return [UPlotModal.$MAX, UPlotModal.$MIN]
  }

  static get key() {
    return 'UPlot'
  }
}

export class UFuncButton extends RootProto<UFuncButton> {
  base = new UObject()
  type = ''
  label = ''
  off_label = ''
  background_color = ''
  text_color = ''
  radius = ''
  font_size = 0.0
  font_weight = 0
  crt_name = ''
  quad_index = 0
  display_name = ''

  constructor(val?: Partial<UFuncButton>) {
    super(UFuncButtonModal, UFuncButton.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UFuncButtonModal.$MAX
  }

  static get minType() {
    return UFuncButtonModal.$MIN
  }

  static get uri() {
    return URI(UFuncButtonModal.$MAX, UFuncButtonModal.$MIN)
  }

  static get types(): [number, number] {
    return [UFuncButtonModal.$MAX, UFuncButtonModal.$MIN]
  }

  static get key() {
    return 'UFuncButton'
  }
}

export class UImage extends RootProto<UImage> {
  base = new UObject()
  image_name = ''

  constructor(val?: Partial<UImage>) {
    super(UImageModal, UImage.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UImageModal.$MAX
  }

  static get minType() {
    return UImageModal.$MIN
  }

  static get uri() {
    return URI(UImageModal.$MAX, UImageModal.$MIN)
  }

  static get types(): [number, number] {
    return [UImageModal.$MAX, UImageModal.$MIN]
  }

  static get key() {
    return 'UImage'
  }
}

export class UInput extends RootProto<UInput> {
  base = new UObject()
  param_id = ''
  label = ''
  label_color = ''
  max = 0.0
  min = 0.0
  inter_val = 0.0
  input_width = 0
  prec = 0
  bar = 0
  left_label = ''
  calc_label = ''
  right_label = ''
  param_box = 0
  param_box_color = ''
  shading = 0
  delta = ''
  digit_font_size = 0.0
  digit_space = 0.0
  label_font_size = 0.0
  label_font_weight = 0
  digit_font_weight = 0
  digit_radius = ''

  constructor(val?: Partial<UInput>) {
    super(UInputModal, UInput.key)
    val && this.assign(val)
  }

  static get maxType() {
    return UInputModal.$MAX
  }

  static get minType() {
    return UInputModal.$MIN
  }

  static get uri() {
    return URI(UInputModal.$MAX, UInputModal.$MIN)
  }

  static get types(): [number, number] {
    return [UInputModal.$MAX, UInputModal.$MIN]
  }

  static get key() {
    return 'UInput'
  }
}

export class ObjectItem extends MapProto<ObjectItem> {
  uri = ''
  bytes = new Uint8Array()

  constructor(val?: Partial<ObjectItem>) {
    super(ObjectItemModal, 'ObjectItem')
    val && this.assign(val)
  }
}

export class Display extends RootProto<Display> {
  name = ''
  background = ''
  editres = ''
  move_grid = 0
  resize_grid = 0
  show_grid = 0
  grid_color = ''
  obj_vec: Array<ObjectItem> = []

  constructor(val?: Partial<Display>) {
    super(DisplayModal, Display.key)
    val && this.assign(val)
  }

  static get maxType() {
    return DisplayModal.$MAX
  }

  static get minType() {
    return DisplayModal.$MIN
  }

  static get uri() {
    return URI(DisplayModal.$MAX, DisplayModal.$MIN)
  }

  static get types(): [number, number] {
    return [DisplayModal.$MAX, DisplayModal.$MIN]
  }

  static get key() {
    return 'Display'
  }
}
