/****
 * 该文件为脚本生成，请勿手动修改
 * 有问题请@movinliao
 */

import {
  RootProto,
  MapProto,
  URI
} from '@wuk/wkp'
import {
  ArincEngine as ArincEngineModal,
  ArincSignal as ArincSignalModal,
  Arinc429Cfg as Arinc429CfgModal
} from '../modal/arinc429.mode'

export class ArincEngine extends MapProto<ArincEngine> {
  name = ''
  eec = ''
  speed = ''
  active = 0

  constructor(val?: Partial<ArincEngine>) {
    super(ArincEngineModal, 'ArincEngine')
    val && this.assign(val)
  }
}

export class ArincSignal extends MapProto<ArincSignal> {
  channel = 0
  busstatusparam = ''
  speed = ''
  parity = ''
  active = 0
  channelid = ''
  engines: Array<ArincEngine> = []

  constructor(val?: Partial<ArincSignal>) {
    super(ArincSignalModal, 'ArincSignal')
    val && this.assign(val)
  }
}

export class Arinc429Cfg extends RootProto<Arinc429Cfg> {
  name = ''
  clock_tic = 0
  nips = 0
  index = 0
  slot = 0
  address = 0
  transmitters: Array<ArincSignal> = []
  receivers: Array<ArincSignal> = []

  constructor(val?: Partial<Arinc429Cfg>) {
    super(Arinc429CfgModal, Arinc429Cfg.key)
    val && this.assign(val)
  }

  static get maxType() {
    return Arinc429CfgModal.$MAX
  }

  static get minType() {
    return Arinc429CfgModal.$MIN
  }

  static get uri() {
    return URI(Arinc429CfgModal.$MAX, Arinc429CfgModal.$MIN)
  }

  static get types(): [number, number] {
    return [Arinc429CfgModal.$MAX, Arinc429CfgModal.$MIN]
  }

  static get key() {
    return 'Arinc429Cfg'
  }
}
