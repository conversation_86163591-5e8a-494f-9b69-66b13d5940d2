import { ProtoClass } from '@wuk/wkp'
import { display } from '../proto'
import { BaseItr, BaseMtr, IBase } from './IBase'
import { CfgFileType } from './type'

export enum DisplayObjectType {
  Box = 'Box',
  Text = 'Text',
  Line = 'Line',
  String = 'String',
  Digital = 'Digital',
  Image = 'Image',
  Input = 'Input',
  FuncButton = 'FuncButton',
  Bar = 'Bar',
  StatusIndicator = 'StatusIndicator',
  Gauge = 'Gauge',
  Switch = 'Switch',
  Button = 'Button',
  Buttonobj = 'Buttonobj',
  Plot = 'Plot'
}

export interface UObject {
  left: number
  top: number
  width: number
  height: number
  font: number
  flag_id: string
  obj_type: string
}

export interface UBox extends UObject {
  style: number
  box_color: string
  shading: number
  line_width: number
  line_color: string
  box_radius: string
}

export interface UText extends UObject {
  direction: number
  alignment: number
  color: string
  value: string
  font_size: number
  font_weight: number
  item_vec: string[]
}

export interface ULine extends UObject {
  line_width: number
  line_color: string
  shading: number
}

export interface StringItem {
  color: string
  op: string
  value: string
  text: string
}

export interface UString extends UObject {
  param_id: string
  label_space: number
  label: string
  label_color: string
  string_color: string
  param_box: number
  param_box_color: string
  shading: number
  string_font_size: number
  string_font_weight: number
  string_radius: string
  label_font_size: number
  label_font_weight: number
  item_vec: Array<StringItem>
}

export interface DigitalItem {
  param_id: string
  type: string
  width: number
  prec: number
  label: string
  units: string
}

export interface UDigital extends UObject {
  label_space: number
  unit_space: number
  spacing: number
  param_box: number
  param_box_color: string
  label_color: string
  shading: number
  direction: number
  font_size: number
  font_weight: number
  label_font_size: number
  label_font_weight: number
  unit_font_size: number
  unit_font_weight: number
  digit_radius: string
  item_vec: Array<DigitalItem>
}

export interface BarItem {
  param_id: string
  target: string
  corr: string
  label: string
  units: string
  min: number
  max: number
  tic_inter: number
  label_inter: number
}

export interface UBar extends UObject {
  label_space: number
  unit_space: number
  label_color: string
  length: number
  bar_height: number
  format: string
  tic_pos: string
  dir: number
  shading: number
  spacing: number
  tic_font: number
  param_box: number
  param_box_color: string
  limit_width: number
  unit_font_size: number
  tic_font_size: number
  digit_font_size: number
  digit_font_weight: number
  digit_radius: string
  label_font_size: number
  label_font_weight: number
  unit_font_weight: number
  bar_arc: number // 0 == 直角显示 | 1 == 圆弧显示
  item_vec: Array<BarItem>
}

export interface IndicatorItem {
  color: string
  op: string
  test_value_id: string
  test_string: string
}

export interface UIndicator extends UObject {
  param_id: string
  text_color: string
  font_size: number
  font_weight: number
  radius: string
  type: string
  label: string
  border_color: string
  border_width: number
  item_vec: Array<IndicatorItem>
}

export interface UGauge extends UObject {
  param_id: string
  gauge_type: string
  peak_param_id: string
  label_color: string
  needle_color: string
  nums_out_side: number
  tic_font: number
  format: string
  max: number
  min: number
  label: string
  units: string
  label_inter: number
  tic_inter: number
  radius: number
  param_box: number
  param_box_color: string
  shading: number
  tic_font_size: number
  axis_line_width: number
  axis_line_space: number
  unit_font_size: number

  digit_font_size: number
  digit_font_weight: number
  digit_radius: string
  label_font_size: number
  label_font_weight: number
  unit_font_weight: number

  gauge_color: string
  track_color: string
  inner_ring_color: string
}

export interface CalMsg {
  uuid: string
  line_num: number
  path: string
}

export interface USwitch extends UObject {
  param_id: string
  type: string
  on_label: string
  off_label: string
  off_color: string
  on_color: string
  hide_box_shadow: number
  radius: string
  font_size: number
  font_weight: number
  has_release: boolean
  release_msg: CalMsg
  release_vec: Array<string>
  has_push: boolean
  push_msg: CalMsg
  push_vec: Array<string>
}

export interface UButton extends UObject {
  type: string
  param_id: string
  set_value: number
  on_label: string
  off_label: string
  label_color: string
  on_color: string
  off_color: string
  radius: string
  font_size: number
  font_weight: number
}

export interface ECMButtonPanel {
  style: number
  line_width: number
  line_color: string
  box_color: string
  shading: number
  label: string
}

export interface ECMButtonState {
  state: number
  label: string
  on_color: string
  has_wait: boolean
  wait_msg: CalMsg
  wait_vec: Array<string>
  has_lock: boolean
  lock_msg: CalMsg
  lock_vec: Array<string>
  has_init: boolean
  init_msg: CalMsg
  init_cal_vec: Array<string>
  has_main: boolean
  main_msg: CalMsg
  main_cal_vec: Array<string>
  has_final: boolean
  final_msg: CalMsg
  final_cal_vec: Array<string>
}

export interface UECMButton extends UObject {
  param_id: string
  wait_param_id: string
  type: string
  label_color: string
  warn_color: string
  off_color: string
  radius: string
  font_size: number
  font_weight: number
  default_state: number
  has_panel: boolean
  panel: ECMButtonPanel
  state_vec: Array<ECMButtonState>
}

export interface XAxis {
  param_id: string
  x_start: string
  x_end: string
  tic_interval: string
  y_intercept: string
  axis_color: string
  label_interval: string
  tic_position: string
  label_position: string
}

export interface ReferenceX {
  y_intercept: number
  tic_position: string
  label_position: string
}

export interface TablePlot {
  color: string
  table_name: string
}

export interface Yaxis {
  param_id: string
  y_start: string
  y_end: string
  tic_interval: string
  x_intercept: string
  axis_color: string
  label_interval: string
  grid: string
  tic_position: string
  label_position: string
  bottom_to_x_axis: string
  top_to_x_axis: string
  color_vec: Array<string>
  table_plot_vec: Array<TablePlot>
}

export interface HardCopyPlotItem {
  position_x: number
  position_y: number
  text_string: string
}

export interface HardCopyPlot {
  pen_number: number
  direction: number
  plot_item_vec: Array<HardCopyPlotItem>
}

export interface UPlot extends UObject {
  plot_key: string
  type: string
  points: Array<string>
  spot_param_id: string
  line_width: number
  has_x_axis: boolean
  x_axis: XAxis
  has_reference_x: boolean
  reference_x: ReferenceX
  yaxis_vec: Array<Yaxis>
  has_hard_copy_plot: boolean
  hard_copy_plot: HardCopyPlot
}

export interface UFuncButton extends UObject {
  type: string
  label: string
  off_label: string
  background_color: string
  text_color: string
  radius: string
  font_size: number
  font_weight: number
  crt_name: string
  quad_index: number
  display_name: string
}

export interface UImage extends UObject {
  image_name: string
}

export interface UInput extends UObject {
  param_id: string
  label: string
  label_color: string
  max: number
  min: number
  inter_val: number
  input_width: number
  prec: number
  bar: number
  left_label: string
  calc_label: string
  right_label: string
  param_box: number
  param_box_color: string
  shading: number
  delta: string
  digit_font_size: number
  digit_space: number
  label_font_size: number
  label_font_weight: number
  digit_font_weight: number
  digit_radius: string
}

export interface UDisplay {
  name: string
  description: string
  background: string
  editres: string
  move_grid: number
  resize_grid: number
  show_grid: number
  grid_color: string
  obj_vec: Array<UObject>
}

export const kDisplayProto: Record<DisplayObjectType, ProtoClass<any> | undefined> = {
  Box: display.UBox,
  Text: display.UText,
  Line: display.ULine,
  String: display.UString,
  Digital: display.UDigital,
  Image: display.UImage,
  Input: display.UInput,
  FuncButton: display.UFuncButton,
  Bar: display.UBar,
  StatusIndicator: display.UIndicator,
  Gauge: display.UGauge,
  Switch: display.USwitch,
  Button: display.UButton,
  Buttonobj: display.UECMButton,
  Plot: display.UPlot
}

export interface DisplayItem {
  /**
   * Name
   */
  name: string
  /**
   * Title
   */
  title: string
  /**
   * Description
   */
  description: string
  /**
   * File
   */
  file: string
  /**
   * Type
   */
  type: CfgFileType
}

export interface DisplayOptions {
  files: DisplayItem[]
}

export abstract class IDisplay extends IBase<IDisplay> {
  abstract readOptions(): Promise<DisplayOptions>
  abstract removeFile(index: number): Promise<boolean>
  abstract addFile(val: DisplayItem, index?: number): Promise<boolean>
  abstract modifyFile(index: number, val: Partial<DisplayItem>): Promise<boolean>

  abstract loadDisplay(index: number): Promise<UDisplay | undefined>
  abstract modifyDisplay(index: number, val: Partial<UDisplay>): Promise<boolean>
  abstract saveDisplay(index: number): Promise<boolean>

  static override get NAME() {
    return 'Display'
  }

  static get OnOptions() {
    return 'Display.OnOptions'
  }

  static get OnFile() {
    return 'Display.OnFile'
  }
}

export type DisplayItr = BaseItr<IDisplay>
export type DisplayMtr = BaseMtr<IDisplay>
