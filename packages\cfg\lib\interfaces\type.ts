export type RecordType = Record<string, any>
export type IBaseProxy = RecordType

export interface PageConfig extends RecordType {
  id: string
  name: string
}

export interface SdkConfig extends RecordType {
  name: string
  path?: string
}

export enum TestHistoryType {
  /**
   * Standard
   */
  Standard = 1,
  /**
   * No History Data
   */
  NoHistoryData = 2
}

export enum CrtResolutionType {
  RL_19201200 = '1920x1200',
  RL_1920x1080 = '1920x1080',
  RL_1600x1200 = '1600x1200',
  RL_1280x1024 = '1280x1024'
}

export const kCrtResolutionVal: Record<string, CrtResolutionType> = {
  WUXGA: CrtResolutionType.RL_19201200,
  FHD: CrtResolutionType.RL_1920x1080,
  HIRES: CrtResolutionType.RL_1600x1200,
  MEDRES: CrtResolutionType.RL_1280x1024
}

export const kCrtValResolution: Record<CrtResolutionType, string> = {
  '1920x1200': 'WUXGA',
  '1920x1080': 'FHD',
  '1600x1200': 'HIRES',
  '1280x1024': 'MEDRES'
}

export enum CrtStatusType {
  Active = 0,
  InActive = 1,
  Control = 2
}

export const kCrtStatusVal: Record<string, CrtStatusType> = {
  CONTROL: CrtStatusType.Control,
  ACTIVE: CrtStatusType.Active,
  INACTIVE: CrtStatusType.InActive
}

export const kCrtValStatus: Record<number, string> = {
  0: 'ACTIVE',
  1: 'INACTIVE',
  2: 'CONTROL'
}

export enum CrtControlType {
  None = 0,
  Touchscreen = 1,
  Nouse = 2,
  Keyboard = 3
}

export const kCrtControlVal: Record<string, CrtControlType> = {
  NONE: CrtControlType.None,
  TOUCHSCREEN: CrtControlType.Touchscreen,
  MOUSE: CrtControlType.Nouse,
  KEYBOARD: CrtControlType.Keyboard
}

export const kCrtValControl: Record<CrtControlType, string> = {
  0: 'NONE',
  1: 'TOUCHSCREEN',
  2: 'MOUSE',
  3: 'KEYBOARD'
}

export enum SignalFilterType {
  Broadband_1 = 1,
  Broadband_2 = 2,
  Broadband_3 = 3,
  Broadband_4 = 4,
  Tracking_1 = 5,
  Tracking_2 = 6,
  Tracking_3 = 7,
  Tracking_4 = 8,
  Tracking_5 = 9,
  Tracking_6 = 10,
  Tracking_7 = 11,
  Tracking_8 = 12,
  Tracking_9 = 13,
  Tracking_10 = 14,
  Tracking_11 = 15,
  Tracking_12 = 16,
  Tracking_13 = 17,
  Tracking_14 = 18,
  Tracking_15 = 19,
  Tracking_16 = 20
}

export enum SignalSigType {
  Amplitude = 0,
  Frequency = 1,
  Phase = 2,
  RPM = 3
}

export enum SignalCalibMode {
  VerifyOnly = 1,
  None = 2
}

export enum InputParamOrderType {
  /**
   * As Listed
   */
  AsListed = 0,
  /**
   * Alphabetical
   */
  Alphabetical = 1,
  /**
   * 1st/3rd
   */
  Fst3rd = 2
}

export const kInputParamOrderVal: Record<string, InputParamOrderType> = {
  AsListed: InputParamOrderType.AsListed,
  Alphabetical: InputParamOrderType.Alphabetical,
  '1st/3rd': InputParamOrderType.Fst3rd
}

export const kCrtValInputParamOrder: Record<InputParamOrderType, string> = {
  0: 'AsListed',
  1: 'Alphabetical',
  2: '1st/3rd'
}

export enum TimeFormatType {
  /**
   * Standard
   */
  Standard = 0,
  /**
   * Military
   */
  Military = 1
}

export const kTimeFormatVal: Record<string, TimeFormatType> = {
  Standard: TimeFormatType.Standard,
  Military: TimeFormatType.Military
}

export const kValTimeFormat: Record<TimeFormatType, string> = {
  0: 'Standard',
  1: 'Military'
}

export enum DateFormatType {
  YYMMDD = 0,
  YYYYMMDD = 1,
  MMDDYY = 2,
  MMDDYYYY = 3,
  DDMMYY = 4,
  DDMMYYYY = 5,
  MM_D_Y = 6
}

export const kDateFormatVal: Record<string, DateFormatType> = {
  YYMMDD: DateFormatType.YYMMDD,
  YYYYMMDD: DateFormatType.YYYYMMDD,
  MMDDYY: DateFormatType.MMDDYY,
  MMDDYYYY: DateFormatType.MMDDYYYY,
  DDMMYY: DateFormatType.DDMMYY,
  DDMMYYYY: DateFormatType.DDMMYYYY,
  MM_D_Y: DateFormatType.MM_D_Y
}

export const kValDateFormat: Record<DateFormatType, string> = {
  0: 'YYMMDD',
  1: 'YYYYMMDD',
  2: 'MMDDYY',
  3: 'MMDDYYYY',
  4: 'DDMMYY',
  5: 'DDMMYYYY',
  6: 'MM_D_Y'
}

export enum TestModeType {
  /**
   * Test With Hardware
   */
  TestWithHardware = 0,
  /**
   * No Hardware
   */
  NoHardware = 1
}

export enum DisplayModeType {
  None = 0,
  PullDown = 1,
  Increment = 2,
  Menu = 3
}

export const kDisplayModeTypeVal: Record<string, DisplayModeType> = {
  None: DisplayModeType.None,
  PullDown: DisplayModeType.PullDown,
  Increment: DisplayModeType.Increment,
  Menu: DisplayModeType.Menu
}

export const kDisplayModeValType: Record<DisplayModeType, string> = {
  0: 'None',
  1: 'PullDown',
  2: 'Increment',
  3: 'Menu'
}

export enum MeasurementRateType {
  Rate_1Hz = 0,
  Rate_2Hz = 1,
  Rate_3Hz = 2,
  Rate_4Hz = 3,
  Rate_5Hz = 4,
  Rate_6Hz = 5,
  Rate_7Hz = 6,
  Rate_8Hz = 7,
  Rate_9Hz = 8,
  Rate_10Hz = 9
}

export enum BandwidthType {
  Band_500Hz = 0,
  Band_1000Hz = 1,
  Band_2000Hz = 2,
  Band_5000Hz = 3,
  Band_10000Hz = 4
}

export enum FFTLinesType {
  FFT_200 = 0,
  FFT_400 = 1,
  FFT_800 = 2,
  FFT_1600 = 3
}

export enum InputType {
  Input_1 = 0,
  Input_2 = 1,
  Input_3 = 2,
  Input_4 = 3
}

export enum TransducerType {
  Transducer_Disabled = 0,
  Transducer_SingleendedAccel = 1,
  Transducer_DifferentialAccel = 2,
  Transducer_Velocity = 3,
  Transducer_ICPAccel = 4
}

export enum AmplifierGainType {
  Gain_AutoSelect = 0,
  Gain_x1 = 1,
  Gain_x10 = 2
}

export enum ADFullScaleType {
  Scale_AutoSelect = 0,
  Scale_125Volts = 1,
  Scale_250Volts = 2,
  Scale_500Volts = 3,
  Scale_1000Volts = 4
}

export enum BroadbanFilterType {
  Broadban_1 = 0,
  Broadban_2 = 1,
  Broadban_3 = 2,
  Broadban_4 = 3
}

export enum OutputUnitsType {
  /**
   * Disabled
   */
  Disabled = 0,
  /**
   * Acceleration(g's)
   */
  Acceleration_Gs = 1,
  /**
   * Velocity(IPS)
   */
  Velocity_IPS = 2,
  /**
   * Displacement(Mils)
   */
  Displacement_Mils = 3,
  /**
   * Acceleration(m/sec2)
   */
  Acceleration_Msec2 = 4,
  /**
   * Displacement(um)
   */
  Displacement_Um = 5
}

export enum BroadFilterType {
  /**
   * No Filtering
   */
  NoFiltering = 0,
  /**
   * Low Pass
   */
  LowPass = 1,
  /**
   * High Pass
   */
  HighPass = 2,
  /**
   * Band Pass
   */
  BandPass = 3
}

export enum FilterShapeType {
  /**
   * Rectangular
   */
  Rectangular = 0,
  /**
   * 7 Pole Chebyshev
   */
  PoleChebyshev_7 = 1,
  /**
   * 6 Pole Butterworth
   */
  PoleButterworth_6 = 2,
  /**
   * User Defined
   */
  UserDefined = 3
}

export enum TimeConstantType {
  /**
   * No Smoothing
   */
  No_Smoothing = 0,
  /**
   * 0.25 Sec Smoothing
   */
  Sec025_Smoothing = 1,
  /**
   * 0.50 Sec Smoothing
   */
  Sec050_Smoothing = 2,
  /**
   * 0.75 Sec Smoothing
   */
  Sec075_Smoothing = 3,
  /**
   * 1.00 Sec Smoothing
   */
  Sec100_Smoothing = 4,
  /**
   * 1.25 Sec Smoothing
   */
  Sec125_Smoothing = 5,
  /**
   * 1.50 Sec Smoothing
   */
  Sec150_Smoothing = 6
}

export enum TachometerInputType {
  Input_1 = 0,
  Input_2 = 1,
  Input_3 = 2,
  Input_4 = 3
}

export enum DetectorType {
  /**
   * Peak
   */
  Peak = 0,
  /**
   * Peak To Peak
   */
  PeakToPeak = 1,
  /**
   * RMS
   */
  RMS = 2,
  /**
   * Average
   */
  Average = 3
}

export enum FilterSpecType {
  /**
   * Constant Bandwidth
   */
  Constant_Bandwidth = 0,
  /**
   * Constant Q
   */
  Constant_Q = 1
}

export enum FilterQType {
  /**
   * Q = 10
   */
  Q_10 = 0,
  /**
   * Q = 20
   */
  Q_20 = 1,
  /**
   * Q = 30
   */
  Q_30 = 2
}

export enum CalcsSignalType {
  None = 0,
  Calibrate = 1
}

export enum TimerType {
  Reset = 0,
  NoReset = 1
}

export const kTimerTypeVal: Record<TimerType, string> = {
  [TimerType.Reset]: 'RESET',
  [TimerType.NoReset]: 'NORESET'
}

export const kTimerType: Record<string, TimerType> = {
  RESET: TimerType.Reset,
  NORESET: TimerType.NoReset
}

export enum CfgFileType {
  Common = 0,
  EngineSpecific = 1,
  Calibrate = 2
}

export enum CalcLineType {
  /**
   * 为空
   */
  kEmpty = 0,
  /**
   * 空行
   */
  kBlank = 1,
  /**
   * 字段
   */
  kField = 2,
  /**
   * 条件 如if
   */
  kCondition = 3,
  /**
   * 跳转
   */
  kGoto = 4,
  /**
   * 返回
   */
  kReturn = 5,
  /**
   * 信号
   */
  kSigcal = 6,
  /**
   * 校准开始
   */
  kCalibBegin = 7,
  /**
   * 校准字段
   */
  kCalibField = 8,
  /**
   * 校准结束
   */
  kCalibEnd = 9
}

export enum CalcWhenToExcuteType {
  kNone = -1,
  kSelectEngine = 0,
  kInitDisplays = 1,
  kOpenTest = 2,
  kLoadTables = 3,
  kAllTimes = 4
}

// 硬件测试选项
export const testMode = {
  // 硬件测试模式
  TestWithDeviceHardware: 'Test With Device Hardware', // 使用设备硬件进行测试 与实际的物理设备硬件交互
  // 模拟测试模式
  NoDeviceHardware: 'No Device Hardware' // 无设备硬件 不连接物理设备硬件
}
export type TestMode = keyof typeof testMode

export const hardwareTypes = {
  // 数据采集设备
  VXI: 'VXI', // VME eXtensions for Instrumentation，模块化测试系统
  MPI322: 'MPI-322', // 多协议接口设备
  MPI320: 'MPI-320', // 多协议接口设备
  DSA3000: 'DSA3000', // 数据采集系统
  DTS3000: 'DTS3000', // 数据采集系统
  PSI9000: 'PSI9000', // 压力传感器接口
  MGCPlus: 'MGCPlus', // 多通道数据采集
  QUANTUMX: 'QUANTUMX', // 高精度数据采集系统
  // 通信接口
  ASCIIDriver: 'ASCII Driver', // 串行通信驱动
  OPC: 'OPC', // OLE for Process Control 工业通信标准
  NAIU: 'NAIU', // 网络接口单元
  // 输出设备
  PRINTER: 'PRINTER', // 打印机输出设备
  PLOTTER: 'PLOTTER', // 绘图仪输出设备
  // 专业设备
  TRUTEMP: 'TRUTEMP', // 温度测量设备
  EX1048: 'EX1048', // 扩展模块
  PBS: 'PBS', // 压力平衡系统
  PI7200: 'PI7200', // 数据记录仪
  // 网络设备
  iServer: 'iServer',
  // 自定义
  CUSTOM: 'CUSTOM'
} as const
export type HardwareType = keyof typeof hardwareTypes

export const psiInterfaceType = {
  // 工业通信标准
  GPIB: 'GPIB', // General Purpose Interface Bus 通用接口总线 常用于测试和测量设备
  Serial: 'Serial', // 串行接口 通常指 RS-232、RS-485 等串行通信端口
  Parallel: 'Parallel', // 并行接口 如 LPT 端口，用于并行数据传输
  LAN: 'LAN' // Local Area Network 局域网 指以太网等网络连接
}
export type PsiInterfaceType = keyof typeof psiInterfaceType

// 打印机/打印技术列表
export const printerType = {
  // 品牌打印机
  EPSON: 'EPSON', // 爱普生（打印机品牌）
  HP2631: 'HP2631', // 惠普2631（特定打印机型号）
  DESKJET2500: 'DESKJET2500', // 惠普DeskJet 2500（特定打印机型号）
  // 打印技术
  LASER: 'LASER', // 激光打印机
  COLORLASER: 'COLORLASER', // 彩色激光打印机
  PAINTJET: 'PAINTJET', // 喷墨打印机（惠普的PaintJet系列）
  DESKJET: 'DESKJET', // 喷墨打印机（惠普的DeskJet系列）
  // 特殊功能
  RUGGEDWRITER: 'RUGGEDWRITER', // 坚固型打印机
  COLORCOPIER: 'COLORCOPIER' // 彩色复印机
}
export type PrinterType = keyof typeof printerType

// VXI 设备/模块列表
export const vxiCardType = {
  CUSTOM: 'CUSTOM',
  // HP设备
  HP_E1458: 'HP_E1458',
  HP_E1332A: 'HP_E1332A',
  HP_E1419A: 'HP_E1419A',
  HP_E1328A: 'HP_E1328A',
  HP_E1413A: 'HP_E1413A',
  HP_E1415: 'HP_E1415',
  HP_E1482A: 'HP_E1482A',
  HP_E1499A: 'HP_E1499A',
  HP_Z2403A: 'HP_Z2403A',
  HP_E1463A: 'HP_E1463A',
  HP_Z2404A: 'HP_Z2404A',
  HP_E1330: 'HP_E1330',
  // CONDOR系列
  CONDOR_429: 'HP_E1419A',
  CONDOR_1553: 'HP_E1419A',
  CONDOR_664: 'HP_E1419A',
  // TM系列
  TM_5410C47: 'TM_5410C47',
  TM_5410C66: 'TM_5410C66',
  TM_5410C89: 'TM_5410C89',
  TM_5410C83: 'TM_5410C83',
  // DAC设备
  DAC32: 'DAC32',
  DAC64: 'DAC64',
  DAC16: 'DAC16',
  // 其他品牌
  METRABYTE: 'METRABYTE',
  RADI: 'RADI',
  ENDEVCO: 'ENDEVCO',
  HBM: 'HBM',
  NOBEL: 'NOBEL',
  DIGITEC: 'DIGITEC',
  BLH: 'BLH',
  GE: 'GE',
  MAXTECH: 'MAXTECH',
  // 特殊功能
  CENCO_TACH: 'CENCO_TACH',
  SCANIVALVE: 'SCANIVALVE',
  UPS_INTERFACE: 'UPS_INTERFACE',
  CCU: 'CCU',
  FMBus: 'FMBus',
  METRABYTE_4141: 'METRABYTE_4141',
  RADI_EPC7: 'RADI_EPC7',
  RPCIE1553: 'RPCIE1553',
  MPI321: 'RPCIE1553',
  GE_FTS: 'RPCIE1553',
  V180: 'RPCIE1553',
  MAXTECH_629: 'RPCIE1553',
  NA_65CS4: 'RPCIE1553',
  NA_65DL2: 'RPCIE1553',
  BC350VXI: 'RPCIE1553'
}
export type VxiCardType = keyof typeof vxiCardType

// 校准模式
export const calibModeType = {
  VerifyOnly: 'Verify Only', // 验证模式
  None: 'None' // 无操作模式
}
export type CalibModeType = keyof typeof calibModeType

// EU 转换选项
export const euConversionType = {
  None: 'None', // 无
  Polynomial: 'Polynomial' // 多项式
}
export type EUConversionType = keyof typeof euConversionType

// Vxi 信号处理记数类型
export const vxiMesurementType = {
  ConstantTerminalEventCount: 'Constant Terminal Event Count', // 恒定终端事件计数
  AutoRangedTerminalEventCount: 'Auto Ranged Terminal Event Count', // 自动范围终端事件计数
  ApertureWindow: 'Aperture Window' // 孔径窗口
}
export type VxiMesurementType = keyof typeof vxiMesurementType

export const arincChannelDataType = {
  BNR: 'BNR',
  BCD: 'BCD',
  DIS: 'DIS',
  ISO: 'ISO',
  ANG: 'ANG',
  HYBBNR: 'HYB-BNR',
  HYBBCD: 'HYB-BCD',
  HYBDIS: 'HYB-DIS',
  HYBISO: 'HYB-ISO',
  HYBANG: 'HYB-ANG'
}
export type ArincChannelDataType = keyof typeof arincChannelDataType

export const arincChannelActiveType = {
  InActive: 'InActive',
  Active: 'Active'
}
export type ArincChannelActiveType = keyof typeof arincChannelActiveType

export const conDorSDIType = {
  SDI00: '00',
  SDI01: '01',
  SDI10: '10',
  SDI11: '11',
  NOSDI: 'NO_SDI'
}
export type ConDorSDIType = keyof typeof conDorSDIType

export const conDorSSMType = {
  SSM00: '00',
  SSM01: '01',
  SSM10: '10',
  SSM11: '11'
}
export type ConDorSSMType = keyof typeof conDorSSMType

export const conDorSpeedType = {
  SLOW: 'SLOW',
  FAST: 'FAST'
}
export type ConDorSpeedType = keyof typeof conDorSpeedType

export const conDorParityType = {
  NONE: 'NONE',
  ODD: 'ODD',
  EVEN: 'EVEN'
}
export type ConDorParityType = keyof typeof conDorParityType

export const conDorActiveType = {
  INACTIVE: 'INACTIVE',
  ACTIVE: 'ACTIVE'
}
export type ConDorActiveType = keyof typeof conDorActiveType

export const conDorOmsUsageType = {
  UNUSED: 'UNUSED',
  TRANSMIT: 'TRANSMIT',
  RECEIVE: 'RECEIVE',
  LOOPBACK: 'LOOPBACK'
}
export type ConDorOmsUsageType = keyof typeof conDorOmsUsageType

// 飞机/发动机配置列表
export const conDorOmsType = {
  NoOMS: 'No OMS', // 无 OMS (Operation and Maintenance System 或 Onboard Maintenance System)
  B777Trent800: 'B777/Trent800', // 波音777飞机，配备罗尔斯·罗伊斯Trent 800系列发动机
  B777GE90: 'B777/GE90', // 波音777飞机，配备通用电气GE90系列发动机
  A330Trent700: 'A330/Trent700', // 空客A330飞机，配备罗尔斯·罗伊斯Trent 700系列发动机
  A340Trent500: 'A340/Trent500' // 空客A340飞机，配备罗尔斯·罗伊斯Trent 500系列发动机
}
export type ConDorOmsType = keyof typeof conDorOmsType

export const conDorChannelType = {
  TRANSMITTER: 'TRANSMITTER',
  RECEIVER: 'RECEIVER'
}
export type ConDorChannelType = keyof typeof conDorChannelType
